/**
 * CarPlay State Test Script
 * Use this to test CarPlay state management functionality
 */

import { NativeModules, NativeEventEmitter } from 'react-native';

class CarPlayStateTester {
  constructor() {
    this.eventEmitter = null;
    this.listeners = [];
    this.testResults = [];
  }

  async runTests() {
    console.log('🧪 Starting CarPlay State Tests...');
    
    try {
      await this.testNativeModuleAvailability();
      await this.testEventEmitterSetup();
      await this.testStateRetrieval();
      await this.testEventListening();
      
      this.printResults();
    } catch (error) {
      console.error('❌ Test failed:', error);
    }
  }

  async testNativeModuleAvailability() {
    console.log('🔍 Testing native module availability...');
    
    const { CarPlayEventEmitter } = NativeModules;
    
    if (CarPlayEventEmitter) {
      this.addResult('✅ CarPlayEventEmitter module available');
      
      if (CarPlayEventEmitter.getCurrentCarPlayState) {
        this.addResult('✅ getCurrentCarPlayState method available');
      } else {
        this.addResult('⚠️ getCurrentCarPlayState method not available');
      }
    } else {
      this.addResult('❌ CarPlayEventEmitter module not available');
      throw new Error('CarPlayEventEmitter not available');
    }
  }

  async testEventEmitterSetup() {
    console.log('🔍 Testing event emitter setup...');
    
    try {
      const { CarPlayEventEmitter } = NativeModules;
      this.eventEmitter = new NativeEventEmitter(CarPlayEventEmitter);
      this.addResult('✅ Event emitter created successfully');
    } catch (error) {
      this.addResult('❌ Failed to create event emitter: ' + error.message);
      throw error;
    }
  }

  async testStateRetrieval() {
    console.log('🔍 Testing state retrieval...');
    
    try {
      const { CarPlayEventEmitter } = NativeModules;
      
      if (CarPlayEventEmitter.getCurrentCarPlayState) {
        const state = await CarPlayEventEmitter.getCurrentCarPlayState();
        this.addResult('✅ State retrieved: ' + JSON.stringify(state));
        
        if (typeof state.isForeground === 'boolean') {
          this.addResult('✅ isForeground property is boolean');
        } else {
          this.addResult('⚠️ isForeground property is not boolean');
        }
        
        if (typeof state.timestamp === 'number') {
          this.addResult('✅ timestamp property is number');
        } else {
          this.addResult('⚠️ timestamp property is not number');
        }
      } else {
        this.addResult('⚠️ getCurrentCarPlayState method not available');
      }
    } catch (error) {
      this.addResult('❌ Failed to retrieve state: ' + error.message);
    }
  }

  async testEventListening() {
    console.log('🔍 Testing event listening...');
    
    return new Promise((resolve) => {
      let eventCount = 0;
      const timeout = setTimeout(() => {
        this.addResult(`⚠️ No events received in 5 seconds (received ${eventCount} events)`);
        this.cleanup();
        resolve();
      }, 5000);

      const events = ['CarPlayForeground', 'CarPlayBackground', 'CarPlayConnection'];
      
      events.forEach(eventName => {
        const listener = this.eventEmitter.addListener(eventName, (data) => {
          eventCount++;
          this.addResult(`✅ Received ${eventName} event: ${JSON.stringify(data)}`);
          
          if (eventCount >= 1) {
            clearTimeout(timeout);
            this.cleanup();
            resolve();
          }
        });
        
        this.listeners.push(listener);
      });

      this.addResult('✅ Event listeners set up for: ' + events.join(', '));
      this.addResult('⏳ Waiting for events (5 second timeout)...');
    });
  }

  addResult(message) {
    this.testResults.push({
      timestamp: new Date().toISOString(),
      message
    });
    console.log(message);
  }

  cleanup() {
    this.listeners.forEach(listener => {
      try {
        listener.remove();
      } catch (error) {
        console.warn('Warning: Failed to remove listener:', error);
      }
    });
    this.listeners = [];
  }

  printResults() {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    const passed = this.testResults.filter(r => r.message.startsWith('✅')).length;
    const warnings = this.testResults.filter(r => r.message.startsWith('⚠️')).length;
    const failed = this.testResults.filter(r => r.message.startsWith('❌')).length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`⚠️ Warnings: ${warnings}`);
    console.log(`❌ Failed: ${failed}`);
    
    console.log('\nDetailed Results:');
    this.testResults.forEach((result, index) => {
      console.log(`${index + 1}. ${result.message}`);
    });
    
    console.log('\n🏁 Tests completed!');
  }
}

// Export for use in your app
export const runCarPlayStateTests = async () => {
  const tester = new CarPlayStateTester();
  await tester.runTests();
};

// Auto-run if this file is imported
if (__DEV__) {
  console.log('🧪 CarPlay State Test Script loaded. Call runCarPlayStateTests() to run tests.');
}

export default CarPlayStateTester;
