#import "CarPlayEventEmitter.h"

@implementation CarPlayEventEmitter {
  bool hasListeners;
  BOOL _isCarPlayForeground;
}

RCT_EXPORT_MODULE();

+ (instancetype)sharedInstance {
  static CarPlayEventEmitter *sharedInstance = nil;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    sharedInstance = [CarPlayEventEmitter new];
  });
  return sharedInstance;
}

- (instancetype)init {
  self = [super init];
  if (self) {
    _isCarPlayForeground = NO;
  }
  return self;
}

- (NSArray<NSString *> *)supportedEvents {
  return @[@"CarPlayForeground", @"CarPlayBackground", @"CarPlayConnection"];
}

- (void)startObserving {
  hasListeners = true;
  NSLog(@"🚗 CarPlayEventEmitter started observing - bridge: %@", self.bridge ? @"available" : @"not available");
}

- (void)stopObserving {
  hasListeners = false;
  NSLog(@"🚗 CarPlayEventEmitter stopped observing");
}

+ (BOOL)requiresMainQueueSetup {
  return YES;
}

- (void)setBridge:(RCTBridge *)bridge {
  [super setBridge:bridge];
  NSLog(@"🚗 CarPlayEventEmitter bridge set: %@", bridge ? @"YES" : @"NO");
}

- (void)sendCarPlayForegroundEvent {
  _isCarPlayForeground = YES;

  // Try to send immediately if bridge is available
  if (hasListeners && self.bridge) {
    NSLog(@"🚗 Sending CarPlayForeground to JS");
    [self sendEventWithName:@"CarPlayForeground" body:@{
      @"isForeground": @(YES),
      @"timestamp": @([[NSDate date] timeIntervalSince1970]),
      @"state": @"foreground"
    }];
  } else {
    NSLog(@"⚠️ Bridge not ready for CarPlayForeground - hasListeners: %d, bridge: %@", hasListeners, self.bridge ? @"YES" : @"NO");

    // Retry after a short delay to allow bridge initialization
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      if (hasListeners && self.bridge) {
        NSLog(@"🚗 Retrying CarPlayForeground to JS");
        [self sendEventWithName:@"CarPlayForeground" body:@{
          @"isForeground": @(YES),
          @"timestamp": @([[NSDate date] timeIntervalSince1970]),
          @"state": @"foreground"
        }];
      } else {
        NSLog(@"⚠️ Bridge still not available for CarPlayForeground after retry");
      }
    });
  }
}

- (void)sendCarPlayBackgroundEvent {
  _isCarPlayForeground = NO;

  // Try to send immediately if bridge is available
  if (hasListeners && self.bridge) {
    NSLog(@"🚗 Sending CarPlayBackground to JS");
    [self sendEventWithName:@"CarPlayBackground" body:@{
      @"isForeground": @(NO),
      @"timestamp": @([[NSDate date] timeIntervalSince1970]),
      @"state": @"background"
    }];
  } else {
    NSLog(@"⚠️ Bridge not ready for CarPlayBackground - hasListeners: %d, bridge: %@", hasListeners, self.bridge ? @"YES" : @"NO");

    // Retry after a short delay to allow bridge initialization
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      if (hasListeners && self.bridge) {
        NSLog(@"🚗 Retrying CarPlayBackground to JS");
        [self sendEventWithName:@"CarPlayBackground" body:@{
          @"isForeground": @(NO),
          @"timestamp": @([[NSDate date] timeIntervalSince1970]),
          @"state": @"background"
        }];
      } else {
        NSLog(@"⚠️ Bridge still not available for CarPlayBackground after retry");
      }
    });
  }
}

- (void)sendCarPlayConnectionEvent:(BOOL)connected {
  // Try to send immediately if bridge is available
  if (hasListeners && self.bridge) {
    NSLog(@"🚗 Sending CarPlayConnection to JS - connected: %@", connected ? @"YES" : @"NO");
    [self sendEventWithName:@"CarPlayConnection" body:@{
      @"connected": @(connected),
      @"timestamp": @([[NSDate date] timeIntervalSince1970])
    }];
  } else {
    NSLog(@"⚠️ Bridge not ready for CarPlayConnection - hasListeners: %d, bridge: %@", hasListeners, self.bridge ? @"YES" : @"NO");

    // Retry after a short delay to allow bridge initialization
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      if (hasListeners && self.bridge) {
        NSLog(@"🚗 Retrying CarPlayConnection to JS - connected: %@", connected ? @"YES" : @"NO");
        [self sendEventWithName:@"CarPlayConnection" body:@{
          @"connected": @(connected),
          @"timestamp": @([[NSDate date] timeIntervalSince1970])
        }];
      } else {
        NSLog(@"⚠️ Bridge still not available for CarPlayConnection after retry");
      }
    });
  }
}

#pragma mark - Exported Methods

RCT_EXPORT_METHOD(getCurrentCarPlayState:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  resolve(@{
    @"isForeground": @(_isCarPlayForeground),
    @"timestamp": @([[NSDate date] timeIntervalSince1970])
  });
}

- (BOOL)isCarPlayForeground {
  return _isCarPlayForeground;
}

@end
