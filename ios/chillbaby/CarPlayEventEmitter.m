#import "CarPlayEventEmitter.h"

@implementation CarPlayEventEmitter {
  bool hasListeners;
  BOOL _isCarPlayForeground;
}

RCT_EXPORT_MODULE();

+ (instancetype)sharedInstance {
  static CarPlayEventEmitter *sharedInstance = nil;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    sharedInstance = [CarPlayEventEmitter new];
  });
  return sharedInstance;
}

- (instancetype)init {
  self = [super init];
  if (self) {
    _isCarPlayForeground = NO;
  }
  return self;
}

- (NSArray<NSString *> *)supportedEvents {
  return @[@"CarPlayForeground", @"CarPlayBackground", @"CarPlayConnection"];
}

- (void)startObserving {
  hasListeners = true;
  NSLog(@"🚗 CarPlayEventEmitter started observing");
}

- (void)stopObserving {
  hasListeners = false;
  NSLog(@"🚗 CarPlayEventEmitter stopped observing");
}

- (void)sendCarPlayForegroundEvent {
  if (hasListeners && self.bridge) {
    _isCarPlayForeground = YES;
    NSLog(@"🚗 Sending CarPlayForeground to JS");
    [self sendEventWithName:@"CarPlayForeground" body:@{
      @"isForeground": @(YES),
      @"timestamp": @([[NSDate date] timeIntervalSince1970]),
      @"state": @"foreground"
    }];
  } else {
    NSLog(@"⚠️ Cannot send CarPlayForeground - hasListeners: %d, bridge: %@", hasListeners, self.bridge ? @"YES" : @"NO");
  }
}

- (void)sendCarPlayBackgroundEvent {
  if (hasListeners && self.bridge) {
    _isCarPlayForeground = NO;
    NSLog(@"🚗 Sending CarPlayBackground to JS");
    [self sendEventWithName:@"CarPlayBackground" body:@{
      @"isForeground": @(NO),
      @"timestamp": @([[NSDate date] timeIntervalSince1970]),
      @"state": @"background"
    }];
  } else {
    NSLog(@"⚠️ Cannot send CarPlayBackground - hasListeners: %d, bridge: %@", hasListeners, self.bridge ? @"YES" : @"NO");
  }
}

- (void)sendCarPlayConnectionEvent:(BOOL)connected {
  if (hasListeners && self.bridge) {
    NSLog(@"🚗 Sending CarPlayConnection to JS - connected: %@", connected ? @"YES" : @"NO");
    [self sendEventWithName:@"CarPlayConnection" body:@{
      @"connected": @(connected),
      @"timestamp": @([[NSDate date] timeIntervalSince1970])
    }];
  } else {
    NSLog(@"⚠️ Cannot send CarPlayConnection - hasListeners: %d, bridge: %@", hasListeners, self.bridge ? @"YES" : @"NO");
  }
}

#pragma mark - Exported Methods

RCT_EXPORT_METHOD(getCurrentCarPlayState:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  resolve(@{
    @"isForeground": @(_isCarPlayForeground),
    @"timestamp": @([[NSDate date] timeIntervalSince1970])
  });
}

- (BOOL)isCarPlayForeground {
  return _isCarPlayForeground;
}

@end
