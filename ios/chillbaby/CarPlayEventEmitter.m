#import "CarPlayEventEmitter.h"

@implementation CarPlayEventEmitter {
  bool hasListeners;
}

RCT_EXPORT_MODULE();

+ (instancetype)sharedInstance {
  static CarPlayEventEmitter *sharedInstance = nil;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    sharedInstance = [CarPlayEventEmitter new];
  });
  return sharedInstance;
}

- (NSArray<NSString *> *)supportedEvents {
  return @[@"CarPlayForeground", @"CarPlayBackground"];
}

- (void)startObserving {
  hasListeners = true;
}

- (void)stopObserving {
  hasListeners = false;
}

- (void)sendCarPlayForegroundEvent {
  if (hasListeners && self.bridge) {
  [self sendEventWithName:@"CarPlayForeground" body:@{@"isForeground": @(YES)}];
}

}

- (void)sendCarPlayBackgroundEvent {
  if (hasListeners) {
    NSLog(@"Sending CarPlayBackground to JS");
    [self sendEventWithName:@"CarPlayBackground" body:@{@"isForeground": @(NO)}];
  }
}

@end
