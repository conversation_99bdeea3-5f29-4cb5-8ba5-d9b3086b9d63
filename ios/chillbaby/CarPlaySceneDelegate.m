#import "CarPlaySceneDelegate.h"
#import "CarPlayEventEmitter.h"
#import "MaxRCTCarPlayNotificationManager/MaxRCTCarPlayNotificationManager.h"
#import <React/RCTBridge.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <React/RCTEventDispatcher.h>
#import "RNCarPlay.h"

@implementation CarPlaySceneDelegate

- (instancetype)init {
    self = [super init];
    if (self) {
        _isCarPlayForeground = NO;
    }
    return self;
}

#pragma mark - CPTemplateApplicationSceneDelegate

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
           didConnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"🚗 CarPlay connected - interface controller received");

    self.interfaceController = interfaceController;
    self.isCarPlayForeground = YES;

    // Tell react-native-carplay CarPlay is connected
    [RNCarPlay connectWithInterfaceController:interfaceController window:templateApplicationScene.carWindow];

    // Note: MaxRCTCarPlayNotificationManager will be set up through React Native bridge
    // We don't need to manually create instances here as it causes bridge issues

    // Send foreground event
    dispatch_async(dispatch_get_main_queue(), ^{
        [[CarPlayEventEmitter sharedInstance] sendCarPlayForegroundEvent];
    });
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
        didDisconnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"🚗 CarPlay disconnected");

    // Clear the interface controller
    self.interfaceController = nil;
    self.isCarPlayForeground = NO;

    // Note: MaxRCTCarPlayNotificationManager will be cleared through React Native bridge
    // We don't need to manually create instances here as it causes bridge issues

    // Notify React Native that CarPlay is disconnected
    [RNCarPlay disconnect];

    // Send background event
    dispatch_async(dispatch_get_main_queue(), ^{
        [[CarPlayEventEmitter sharedInstance] sendCarPlayBackgroundEvent];
    });
}

#pragma mark - Scene Lifecycle (iOS 13+)

- (void)sceneDidBecomeActive:(UIScene *)scene {
    if ([scene isKindOfClass:[CPTemplateApplicationScene class]]) {
        NSLog(@"🚗 CarPlay scene became active (foreground)");
        self.isCarPlayForeground = YES;

        dispatch_async(dispatch_get_main_queue(), ^{
            [[CarPlayEventEmitter sharedInstance] sendCarPlayForegroundEvent];
        });
    }
}

- (void)sceneWillResignActive:(UIScene *)scene {
    if ([scene isKindOfClass:[CPTemplateApplicationScene class]]) {
        NSLog(@"🚗 CarPlay scene will resign active (background)");
        self.isCarPlayForeground = NO;

        dispatch_async(dispatch_get_main_queue(), ^{
            [[CarPlayEventEmitter sharedInstance] sendCarPlayBackgroundEvent];
        });
    }
}

- (void)sceneDidEnterBackground:(UIScene *)scene {
    if ([scene isKindOfClass:[CPTemplateApplicationScene class]]) {
        NSLog(@"🚗 CarPlay scene entered background");
        self.isCarPlayForeground = NO;

        dispatch_async(dispatch_get_main_queue(), ^{
            [[CarPlayEventEmitter sharedInstance] sendCarPlayBackgroundEvent];
        });
    }
}

- (void)sceneWillEnterForeground:(UIScene *)scene {
    if ([scene isKindOfClass:[CPTemplateApplicationScene class]]) {
        NSLog(@"🚗 CarPlay scene will enter foreground");
        self.isCarPlayForeground = YES;

        dispatch_async(dispatch_get_main_queue(), ^{
            [[CarPlayEventEmitter sharedInstance] sendCarPlayForegroundEvent];
        });
    }
}

#pragma mark - Public Methods

- (BOOL)isCarPlayInForeground {
    return self.isCarPlayForeground && self.interfaceController != nil;
}

@end
