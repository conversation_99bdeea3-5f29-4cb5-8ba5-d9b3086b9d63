#import "CarPlaySceneDelegate.h"
#import "CarPlayEventEmitter.h"
#import <React/RCTBridge.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <React/RCTEventDispatcher.h>
#import "RNCarPlay.h"  

@implementation CarPlaySceneDelegate

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
           didConnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay connected - interface controller received");

    self.interfaceController = interfaceController;

    // Tell react-native-carplay CarPlay is connected
    [RNCarPlay connectWithInterfaceController:interfaceController window:templateApplicationScene.carWindow];
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
        didDisconnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay disconnected");

    // Clear the interface controller
    self.interfaceController = nil;

    // Notify React Native that CarPlay is disconnected
    [RNCarPlay disconnect];
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
     didConnectInterfaceController:(CPInterfaceController *)interfaceController
                          toWindow:(CPWindow *)window {
  self.interfaceController = interfaceController;

  NSLog(@"✅ CarPlay connected (foreground)");
  dispatch_async(dispatch_get_main_queue(), ^{
    [[CarPlayEventEmitter sharedInstance] sendCarPlayForegroundEvent];
  });
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
  didDisconnectInterfaceController:(CPInterfaceController *)interfaceController
                         fromWindow:(CPWindow *)window {
  self.interfaceController = nil;

  NSLog(@"✅ CarPlay disconnected (background)");
  dispatch_async(dispatch_get_main_queue(), ^{
    [[CarPlayEventEmitter sharedInstance] sendCarPlayBackgroundEvent];
  });
}


@end
