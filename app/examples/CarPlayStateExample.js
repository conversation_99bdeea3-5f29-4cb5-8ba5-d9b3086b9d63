/**
 * CarPlay State Example
 * Demonstrates how to use the new CarPlay state management system
 */

import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { useCarPlayState, useCarPlayForeground, useCarPlayConnection } from '../hooks/useCarPlayState';
import CarPlayStatusIndicator from '../components/CarPlayStatusIndicator';

const CarPlayStateExample = () => {
  // Method 1: Use the full hook
  const {
    isForeground,
    isConnected,
    isBackground,
    timestamp,
    lastStateChange,
    refreshState,
    getCarPlayInfo,
    carPlayState
  } = useCarPlayState();

  // Method 2: Use simple hooks for specific states
  const isCarPlayForeground = useCarPlayForeground();
  const isCarPlayConnected = useCarPlayConnection();

  // Handle state changes
  useEffect(() => {
    if (lastStateChange) {
      console.log('🚗 CarPlay state changed at:', new Date(lastStateChange).toLocaleTimeString());
      
      // You can perform actions based on state changes here
      if (isForeground) {
        console.log('✅ CarPlay is now in foreground - show CarPlay UI');
        // Show CarPlay-specific UI or notifications
      } else if (isBackground) {
        console.log('⚠️ CarPlay is now in background - hide CarPlay UI');
        // Hide CarPlay-specific UI
      }
    }
  }, [lastStateChange, isForeground, isBackground]);

  // Handle connection changes
  useEffect(() => {
    if (isConnected) {
      console.log('🔌 CarPlay connected - initialize CarPlay features');
      // Initialize CarPlay-specific features
    } else {
      console.log('🔌 CarPlay disconnected - cleanup CarPlay features');
      // Cleanup CarPlay-specific features
    }
  }, [isConnected]);

  const handleShowDetailedInfo = async () => {
    const info = getCarPlayInfo();
    const currentState = await refreshState();
    
    Alert.alert(
      'CarPlay Information',
      `Version: ${info.version}\n` +
      `iOS Target: ${info.iosDeploymentTarget}\n` +
      `Connected: ${info.isConnected ? 'Yes' : 'No'}\n` +
      `Foreground: ${info.isForeground ? 'Yes' : 'No'}\n` +
      `Supported Events: ${info.supportedEvents.join(', ')}\n` +
      `Last Update: ${new Date(info.timestamp * 1000).toLocaleString()}`
    );
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>CarPlay State Management Example</Text>
      
      {/* Status Indicator Component */}
      <CarPlayStatusIndicator showDebugInfo={true} />
      
      {/* State Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current State</Text>
        <View style={styles.stateRow}>
          <Text style={styles.label}>Connected:</Text>
          <Text style={[styles.value, { color: isConnected ? '#28A745' : '#DC3545' }]}>
            {isConnected ? 'Yes' : 'No'}
          </Text>
        </View>
        <View style={styles.stateRow}>
          <Text style={styles.label}>Foreground:</Text>
          <Text style={[styles.value, { color: isForeground ? '#28A745' : '#FFC107' }]}>
            {isForeground ? 'Yes' : 'No'}
          </Text>
        </View>
        <View style={styles.stateRow}>
          <Text style={styles.label}>Background:</Text>
          <Text style={[styles.value, { color: isBackground ? '#FFC107' : '#28A745' }]}>
            {isBackground ? 'Yes' : 'No'}
          </Text>
        </View>
        {timestamp && (
          <View style={styles.stateRow}>
            <Text style={styles.label}>Last Update:</Text>
            <Text style={styles.value}>
              {new Date(timestamp * 1000).toLocaleTimeString()}
            </Text>
          </View>
        )}
      </View>

      {/* Simple Hook Results */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Simple Hooks</Text>
        <Text style={styles.hookResult}>
          useCarPlayForeground(): {isCarPlayForeground ? 'true' : 'false'}
        </Text>
        <Text style={styles.hookResult}>
          useCarPlayConnection(): {isCarPlayConnected ? 'true' : 'false'}
        </Text>
      </View>

      {/* Usage Examples */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Usage Examples</Text>
        <Text style={styles.exampleText}>
          1. Use useCarPlayState() for full state management{'\n'}
          2. Use useCarPlayForeground() for simple foreground detection{'\n'}
          3. Use useCarPlayConnection() for simple connection detection{'\n'}
          4. Listen to state changes with useEffect{'\n'}
          5. Use CarPlayStatusIndicator component for UI
        </Text>
      </View>

      {/* Raw State Object */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Raw State Object</Text>
        <Text style={styles.jsonText}>
          {JSON.stringify(carPlayState, null, 2)}
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    margin: 20,
    color: '#343A40',
  },
  section: {
    backgroundColor: 'white',
    margin: 10,
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#495057',
  },
  stateRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    color: '#6C757D',
  },
  value: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  hookResult: {
    fontSize: 14,
    fontFamily: 'monospace',
    backgroundColor: '#F8F9FA',
    padding: 8,
    borderRadius: 4,
    marginBottom: 5,
  },
  exampleText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#6C757D',
  },
  jsonText: {
    fontSize: 12,
    fontFamily: 'monospace',
    backgroundColor: '#F8F9FA',
    padding: 10,
    borderRadius: 4,
    color: '#495057',
  },
});

export default CarPlayStateExample;
