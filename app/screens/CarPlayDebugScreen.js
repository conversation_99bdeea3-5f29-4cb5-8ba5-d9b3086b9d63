/**
 * CarPlay Debug Screen
 * A debug screen to test and monitor CarPlay functionality
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { useCarPlayState } from '../hooks/useCarPlayState';
import CarPlayStatusIndicator from '../components/CarPlayStatusIndicator';
import carPlayDebugger from '../utils/CarPlayDebugger';

const CarPlayDebugScreen = () => {
  const [logs, setLogs] = useState([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [stopMonitoring, setStopMonitoring] = useState(null);
  
  const carPlayState = useCarPlayState();

  useEffect(() => {
    // Initial log load
    setLogs(carPlayDebugger.getLogs());
  }, []);

  const handleRunDiagnostic = async () => {
    try {
      setRefreshing(true);
      const diagnosticLogs = await carPlayDebugger.runFullDiagnostic();
      setLogs(diagnosticLogs);
    } catch (error) {
      Alert.alert('Error', `Failed to run diagnostic: ${error.message}`);
    } finally {
      setRefreshing(false);
    }
  };

  const handleStartMonitoring = () => {
    if (isMonitoring) {
      // Stop monitoring
      if (stopMonitoring) {
        stopMonitoring();
        setStopMonitoring(null);
      }
      setIsMonitoring(false);
    } else {
      // Start monitoring
      const stopFn = carPlayDebugger.monitorEvents(30000); // 30 seconds
      setStopMonitoring(() => stopFn);
      setIsMonitoring(true);
      
      // Auto-stop after 30 seconds
      setTimeout(() => {
        setIsMonitoring(false);
        setStopMonitoring(null);
      }, 30000);
    }
  };

  const handleClearLogs = () => {
    carPlayDebugger.clearLogs();
    setLogs([]);
  };

  const handleExportLogs = () => {
    const logText = carPlayDebugger.exportLogs();
    Alert.alert(
      'Export Logs',
      'Logs have been copied to console. Check your development console.',
      [{ text: 'OK' }]
    );
    console.log('=== CARPLAY DEBUG LOGS ===\n' + logText);
  };

  const handleRefresh = () => {
    setLogs(carPlayDebugger.getLogs());
  };

  const getLogColor = (type) => {
    switch (type) {
      case 'error': return '#DC3545';
      case 'warning': return '#FFC107';
      default: return '#28A745';
    }
  };

  const getLogIcon = (type) => {
    switch (type) {
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return '🚗';
    }
  };

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      <Text style={styles.title}>CarPlay Debug Console</Text>
      
      {/* Status Indicator */}
      <CarPlayStatusIndicator showDebugInfo={true} />
      
      {/* Current State Summary */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current State</Text>
        <Text style={styles.stateText}>
          Connected: {carPlayState.isConnected ? '✅ Yes' : '❌ No'}
        </Text>
        <Text style={styles.stateText}>
          Foreground: {carPlayState.isForeground ? '✅ Yes' : '⚠️ No'}
        </Text>
        <Text style={styles.stateText}>
          Loading: {carPlayState.isLoading ? '⏳ Yes' : '✅ No'}
        </Text>
        {carPlayState.timestamp && (
          <Text style={styles.stateText}>
            Last Update: {new Date(carPlayState.timestamp * 1000).toLocaleTimeString()}
          </Text>
        )}
      </View>
      
      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, styles.primaryButton]} 
          onPress={handleRunDiagnostic}
          disabled={refreshing}
        >
          <Text style={styles.buttonText}>
            {refreshing ? 'Running...' : 'Run Diagnostic'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, isMonitoring ? styles.dangerButton : styles.secondaryButton]} 
          onPress={handleStartMonitoring}
        >
          <Text style={styles.buttonText}>
            {isMonitoring ? 'Stop Monitoring' : 'Monitor Events'}
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, styles.warningButton]} 
          onPress={handleClearLogs}
        >
          <Text style={styles.buttonText}>Clear Logs</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.infoButton]} 
          onPress={handleExportLogs}
        >
          <Text style={styles.buttonText}>Export Logs</Text>
        </TouchableOpacity>
      </View>
      
      {/* Logs Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Debug Logs ({logs.length})
          {isMonitoring && <Text style={styles.monitoringText}> - Monitoring...</Text>}
        </Text>
        
        {logs.length === 0 ? (
          <Text style={styles.noLogsText}>No logs available. Run diagnostic to generate logs.</Text>
        ) : (
          <ScrollView style={styles.logsContainer} nestedScrollEnabled={true}>
            {logs.map((log, index) => (
              <View key={index} style={styles.logEntry}>
                <Text style={[styles.logText, { color: getLogColor(log.type) }]}>
                  {getLogIcon(log.type)} {log.message}
                </Text>
                <Text style={styles.logTimestamp}>
                  {new Date(log.timestamp).toLocaleTimeString()}
                </Text>
              </View>
            ))}
          </ScrollView>
        )}
      </View>
      
      {/* Instructions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Instructions</Text>
        <Text style={styles.instructionText}>
          1. Run Diagnostic to check all CarPlay modules{'\n'}
          2. Monitor Events to watch real-time CarPlay events{'\n'}
          3. Connect/disconnect CarPlay to test functionality{'\n'}
          4. Switch between apps in CarPlay to test foreground/background{'\n'}
          5. Check logs for any bridge or event issues
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    margin: 20,
    color: '#343A40',
  },
  section: {
    backgroundColor: 'white',
    margin: 10,
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#495057',
  },
  stateText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#6C757D',
  },
  buttonContainer: {
    flexDirection: 'row',
    marginHorizontal: 10,
    marginVertical: 5,
    gap: 10,
  },
  button: {
    flex: 1,
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#007BFF',
  },
  secondaryButton: {
    backgroundColor: '#6C757D',
  },
  dangerButton: {
    backgroundColor: '#DC3545',
  },
  warningButton: {
    backgroundColor: '#FFC107',
  },
  infoButton: {
    backgroundColor: '#17A2B8',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  monitoringText: {
    color: '#28A745',
    fontWeight: 'normal',
    fontSize: 14,
  },
  logsContainer: {
    maxHeight: 300,
    backgroundColor: '#F8F9FA',
    borderRadius: 4,
    padding: 10,
  },
  logEntry: {
    marginBottom: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E9ECEF',
  },
  logText: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
  logTimestamp: {
    fontSize: 10,
    color: '#ADB5BD',
    marginTop: 2,
  },
  noLogsText: {
    textAlign: 'center',
    color: '#6C757D',
    fontStyle: 'italic',
    padding: 20,
  },
  instructionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#6C757D',
  },
});

export default CarPlayDebugScreen;
