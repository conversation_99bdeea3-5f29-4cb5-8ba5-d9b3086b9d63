/**
 * CarPlay State Manager
 * Manages CarPlay foreground/background state and provides easy access to CarPlay status
 */

import { NativeEventEmitter, NativeModules } from 'react-native';

class CarPlayStateManager {
  constructor() {
    this.isCarPlayForeground = false;
    this.isCarPlayConnected = false;
    this.listeners = [];
    this.eventEmitter = null;
    
    this.setupEventListeners();
  }

  setupEventListeners() {
    try {
      const { CarPlayEventEmitter } = NativeModules;
      
      if (CarPlayEventEmitter) {
        this.eventEmitter = new NativeEventEmitter(CarPlayEventEmitter);
        
        // Listen for CarPlay foreground events
        this.eventEmitter.addListener('CarPlayForeground', (data) => {
          console.log('🚗 CarPlay entered foreground:', data);
          this.isCarPlayForeground = true;
          this.notifyListeners('foreground', data);
        });

        // Listen for CarPlay background events
        this.eventEmitter.addListener('CarPlayBackground', (data) => {
          console.log('🚗 CarPlay entered background:', data);
          this.isCarPlayForeground = false;
          this.notifyListeners('background', data);
        });

        // Listen for CarPlay connection events
        this.eventEmitter.addListener('CarPlayConnection', (data) => {
          console.log('🚗 CarPlay connection changed:', data);
          this.isCarPlayConnected = data.connected;
          this.notifyListeners('connection', data);
        });

        console.log('✅ CarPlay State Manager initialized successfully');
      } else {
        console.warn('⚠️ CarPlayEventEmitter not available');
      }
    } catch (error) {
      console.error('❌ Error setting up CarPlay event listeners:', error);
    }
  }

  /**
   * Get current CarPlay state
   * @returns {Promise<Object>} Current state object
   */
  async getCurrentState() {
    try {
      const { CarPlayEventEmitter } = NativeModules;
      if (CarPlayEventEmitter && CarPlayEventEmitter.getCurrentCarPlayState) {
        const state = await CarPlayEventEmitter.getCurrentCarPlayState();
        this.isCarPlayForeground = state.isForeground;
        return {
          isForeground: state.isForeground,
          isConnected: this.isCarPlayConnected,
          timestamp: state.timestamp
        };
      }
    } catch (error) {
      console.error('❌ Error getting CarPlay state:', error);
    }
    
    return {
      isForeground: this.isCarPlayForeground,
      isConnected: this.isCarPlayConnected,
      timestamp: Date.now() / 1000
    };
  }

  /**
   * Check if CarPlay is currently in foreground
   * @returns {boolean}
   */
  isInForeground() {
    return this.isCarPlayForeground;
  }

  /**
   * Check if CarPlay is currently connected
   * @returns {boolean}
   */
  isConnected() {
    return this.isCarPlayConnected;
  }

  /**
   * Add a listener for CarPlay state changes
   * @param {Function} callback - Callback function (state, data) => {}
   * @returns {Function} Unsubscribe function
   */
  addStateListener(callback) {
    this.listeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners of state changes
   * @private
   */
  notifyListeners(state, data) {
    this.listeners.forEach(callback => {
      try {
        callback(state, data);
      } catch (error) {
        console.error('❌ Error in CarPlay state listener:', error);
      }
    });
  }

  /**
   * Clean up event listeners
   */
  cleanup() {
    if (this.eventEmitter) {
      this.eventEmitter.removeAllListeners('CarPlayForeground');
      this.eventEmitter.removeAllListeners('CarPlayBackground');
      this.eventEmitter.removeAllListeners('CarPlayConnection');
    }
    this.listeners = [];
  }

  /**
   * Get detailed CarPlay information
   * @returns {Object} Detailed CarPlay info
   */
  getCarPlayInfo() {
    return {
      isForeground: this.isCarPlayForeground,
      isConnected: this.isCarPlayConnected,
      version: '1.0.2',
      iosDeploymentTarget: '13.4',
      supportedEvents: ['CarPlayForeground', 'CarPlayBackground', 'CarPlayConnection'],
      timestamp: Date.now() / 1000
    };
  }
}

// Create singleton instance
const carPlayStateManager = new CarPlayStateManager();

export default carPlayStateManager;

// Export individual methods for convenience
export const {
  getCurrentState,
  isInForeground,
  isConnected,
  addStateListener,
  getCarPlayInfo
} = carPlayStateManager;
