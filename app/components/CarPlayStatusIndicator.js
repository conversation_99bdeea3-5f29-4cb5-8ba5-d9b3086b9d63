/**
 * CarPlay Status Indicator Component
 * Shows current CarPlay state and provides debugging information
 */

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useCarPlayState } from '../hooks/useCarPlayState';

const CarPlayStatusIndicator = ({ showDebugInfo = false }) => {
  const {
    isForeground,
    isConnected,
    isBackground,
    timestamp,
    lastStateChange,
    isLoading,
    refreshState,
    getCarPlayInfo,
    carPlayState
  } = useCarPlayState();

  const handleRefresh = async () => {
    console.log('🔄 Refreshing CarPlay state...');
    await refreshState();
  };

  const handleShowInfo = () => {
    const info = getCarPlayInfo();
    console.log('🚗 CarPlay Info:', info);
    alert(`CarPlay Info:\n${JSON.stringify(info, null, 2)}`);
  };

  const getStatusColor = () => {
    if (!isConnected) return '#FF6B6B'; // Red for disconnected
    if (isForeground) return '#4ECDC4'; // Teal for foreground
    return '#FFE66D'; // Yellow for background
  };

  const getStatusText = () => {
    if (!isConnected) return 'Disconnected';
    if (isForeground) return 'Foreground';
    return 'Background';
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading CarPlay state...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={[styles.statusIndicator, { backgroundColor: getStatusColor() }]}>
        <Text style={styles.statusText}>CarPlay: {getStatusText()}</Text>
      </View>
      
      {showDebugInfo && (
        <View style={styles.debugContainer}>
          <Text style={styles.debugTitle}>Debug Info:</Text>
          <Text style={styles.debugText}>Connected: {isConnected ? 'Yes' : 'No'}</Text>
          <Text style={styles.debugText}>Foreground: {isForeground ? 'Yes' : 'No'}</Text>
          <Text style={styles.debugText}>Background: {isBackground ? 'Yes' : 'No'}</Text>
          {timestamp && (
            <Text style={styles.debugText}>
              Last Update: {new Date(timestamp * 1000).toLocaleTimeString()}
            </Text>
          )}
          {lastStateChange && (
            <Text style={styles.debugText}>
              Last Change: {new Date(lastStateChange).toLocaleTimeString()}
            </Text>
          )}
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.button} onPress={handleRefresh}>
              <Text style={styles.buttonText}>Refresh</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.button} onPress={handleShowInfo}>
              <Text style={styles.buttonText}>Show Info</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    margin: 10,
  },
  statusIndicator: {
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 10,
  },
  statusText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  loadingText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
  },
  debugContainer: {
    backgroundColor: '#FFFFFF',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  debugTitle: {
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 8,
    color: '#495057',
  },
  debugText: {
    fontSize: 12,
    color: '#6C757D',
    marginBottom: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: 10,
    gap: 10,
  },
  button: {
    backgroundColor: '#007BFF',
    padding: 8,
    borderRadius: 4,
    flex: 1,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default CarPlayStatusIndicator;
