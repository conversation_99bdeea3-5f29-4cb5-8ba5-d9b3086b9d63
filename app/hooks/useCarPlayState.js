/**
 * useCarPlayState Hook
 * React hook for managing CarPlay state in components
 */

import { useState, useEffect, useCallback } from 'react';
import carPlayStateManager from '../services/CarPlayStateManager';

/**
 * Custom hook for CarPlay state management
 * @returns {Object} CarPlay state and methods
 */
export const useCarPlayState = () => {
  const [carPlayState, setCarPlayState] = useState({
    isForeground: false,
    isConnected: false,
    timestamp: null,
    lastStateChange: null
  });

  const [isLoading, setIsLoading] = useState(true);

  // Update state from manager
  const updateState = useCallback(async () => {
    try {
      const currentState = await carPlayStateManager.getCurrentState();
      setCarPlayState(prevState => ({
        ...currentState,
        lastStateChange: prevState.isForeground !== currentState.isForeground ? Date.now() : prevState.lastStateChange
      }));
    } catch (error) {
      console.error('❌ Error updating CarPlay state:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Handle state changes
  const handleStateChange = useCallback((state, data) => {
    console.log(`🚗 CarPlay state changed to: ${state}`, data);
    
    setCarPlayState(prevState => ({
      isForeground: state === 'foreground' ? true : state === 'background' ? false : prevState.isForeground,
      isConnected: state === 'connection' ? data.connected : prevState.isConnected,
      timestamp: data.timestamp || Date.now() / 1000,
      lastStateChange: Date.now()
    }));
  }, []);

  useEffect(() => {
    // Initial state load
    updateState();

    // Add state listener
    const unsubscribe = carPlayStateManager.addStateListener(handleStateChange);

    // Cleanup on unmount
    return () => {
      unsubscribe();
    };
  }, [updateState, handleStateChange]);

  // Refresh state manually
  const refreshState = useCallback(async () => {
    setIsLoading(true);
    await updateState();
  }, [updateState]);

  // Get detailed CarPlay info
  const getCarPlayInfo = useCallback(() => {
    return carPlayStateManager.getCarPlayInfo();
  }, []);

  return {
    // State
    isForeground: carPlayState.isForeground,
    isConnected: carPlayState.isConnected,
    isBackground: !carPlayState.isForeground,
    timestamp: carPlayState.timestamp,
    lastStateChange: carPlayState.lastStateChange,
    isLoading,
    
    // Methods
    refreshState,
    getCarPlayInfo,
    
    // Full state object
    carPlayState
  };
};

/**
 * Hook for simple CarPlay foreground detection
 * @returns {boolean} Whether CarPlay is in foreground
 */
export const useCarPlayForeground = () => {
  const { isForeground } = useCarPlayState();
  return isForeground;
};

/**
 * Hook for simple CarPlay connection detection
 * @returns {boolean} Whether CarPlay is connected
 */
export const useCarPlayConnection = () => {
  const { isConnected } = useCarPlayState();
  return isConnected;
};

/**
 * Hook for CarPlay state with callback
 * @param {Function} onStateChange - Callback for state changes
 * @returns {Object} CarPlay state
 */
export const useCarPlayStateWithCallback = (onStateChange) => {
  const carPlayState = useCarPlayState();
  
  useEffect(() => {
    if (onStateChange && carPlayState.lastStateChange) {
      onStateChange(carPlayState);
    }
  }, [carPlayState.lastStateChange, onStateChange, carPlayState]);
  
  return carPlayState;
};

export default useCarPlayState;
