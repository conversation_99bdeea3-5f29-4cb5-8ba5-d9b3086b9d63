/**
 * CarPlay Debugger Utility
 * Helps debug CarPlay bridge and event issues
 */

import { NativeModules, NativeEventEmitter } from 'react-native';

class CarPlayDebugger {
  constructor() {
    this.logs = [];
    this.maxLogs = 100;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      message,
      type
    };
    
    this.logs.unshift(logEntry);
    if (this.logs.length > this.maxLogs) {
      this.logs.pop();
    }
    
    const emoji = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : '🚗';
    console.log(`${emoji} [CarPlay] ${message}`);
  }

  checkNativeModules() {
    this.log('=== Checking Native Modules ===');
    
    const { CarPlayEventEmitter, MaxRCTCarPlayNotificationManager, RNCarPlay } = NativeModules;
    
    // Check CarPlayEventEmitter
    if (CarPlayEventEmitter) {
      this.log('✅ CarPlayEventEmitter module available');
      
      if (CarPlayEventEmitter.getCurrentCarPlayState) {
        this.log('✅ getCurrentCarPlayState method available');
      } else {
        this.log('⚠️ getCurrentCarPlayState method not available', 'warning');
      }
    } else {
      this.log('❌ CarPlayEventEmitter module not available', 'error');
    }
    
    // Check MaxRCTCarPlayNotificationManager
    if (MaxRCTCarPlayNotificationManager) {
      this.log('✅ MaxRCTCarPlayNotificationManager module available');
      
      if (MaxRCTCarPlayNotificationManager.isCarPlayAvailable) {
        this.log('✅ isCarPlayAvailable method available');
      } else {
        this.log('⚠️ isCarPlayAvailable method not available', 'warning');
      }
    } else {
      this.log('❌ MaxRCTCarPlayNotificationManager module not available', 'error');
    }
    
    // Check RNCarPlay
    if (RNCarPlay) {
      this.log('✅ RNCarPlay module available');
    } else {
      this.log('❌ RNCarPlay module not available', 'error');
    }
    
    this.log('=== Module Check Complete ===');
  }

  async testEventEmitters() {
    this.log('=== Testing Event Emitters ===');
    
    try {
      const { CarPlayEventEmitter, MaxRCTCarPlayNotificationManager } = NativeModules;
      
      if (CarPlayEventEmitter) {
        const emitter = new NativeEventEmitter(CarPlayEventEmitter);
        this.log('✅ CarPlayEventEmitter event emitter created');
        
        // Test event listeners
        const testListener = emitter.addListener('CarPlayForeground', (data) => {
          this.log(`✅ Received CarPlayForeground event: ${JSON.stringify(data)}`);
        });
        
        setTimeout(() => {
          testListener.remove();
          this.log('✅ Test listener removed');
        }, 1000);
      }
      
      if (MaxRCTCarPlayNotificationManager) {
        const notificationEmitter = new NativeEventEmitter(MaxRCTCarPlayNotificationManager);
        this.log('✅ MaxRCTCarPlayNotificationManager event emitter created');
      }
      
    } catch (error) {
      this.log(`❌ Error testing event emitters: ${error.message}`, 'error');
    }
    
    this.log('=== Event Emitter Test Complete ===');
  }

  async testCarPlayState() {
    this.log('=== Testing CarPlay State ===');
    
    try {
      const { CarPlayEventEmitter } = NativeModules;
      
      if (CarPlayEventEmitter && CarPlayEventEmitter.getCurrentCarPlayState) {
        const state = await CarPlayEventEmitter.getCurrentCarPlayState();
        this.log(`✅ Current CarPlay state: ${JSON.stringify(state)}`);
        
        if (typeof state.isForeground === 'boolean') {
          this.log('✅ isForeground property is valid boolean');
        } else {
          this.log('⚠️ isForeground property is not boolean', 'warning');
        }
        
        if (typeof state.timestamp === 'number') {
          this.log('✅ timestamp property is valid number');
        } else {
          this.log('⚠️ timestamp property is not number', 'warning');
        }
      } else {
        this.log('⚠️ getCurrentCarPlayState method not available', 'warning');
      }
    } catch (error) {
      this.log(`❌ Error testing CarPlay state: ${error.message}`, 'error');
    }
    
    this.log('=== CarPlay State Test Complete ===');
  }

  async testNotificationManager() {
    this.log('=== Testing Notification Manager ===');
    
    try {
      const { MaxRCTCarPlayNotificationManager } = NativeModules;
      
      if (MaxRCTCarPlayNotificationManager) {
        // Test connection status
        if (MaxRCTCarPlayNotificationManager.getConnectionStatus) {
          const status = await MaxRCTCarPlayNotificationManager.getConnectionStatus();
          this.log(`✅ Connection status: ${JSON.stringify(status)}`);
        }
        
        // Test availability
        if (MaxRCTCarPlayNotificationManager.isCarPlayAvailable) {
          const availability = await MaxRCTCarPlayNotificationManager.isCarPlayAvailable();
          this.log(`✅ CarPlay availability: ${JSON.stringify(availability)}`);
        }
      } else {
        this.log('⚠️ MaxRCTCarPlayNotificationManager not available', 'warning');
      }
    } catch (error) {
      this.log(`❌ Error testing notification manager: ${error.message}`, 'error');
    }
    
    this.log('=== Notification Manager Test Complete ===');
  }

  async runFullDiagnostic() {
    this.log('🚗 Starting CarPlay Full Diagnostic...');
    
    this.checkNativeModules();
    await this.testEventEmitters();
    await this.testCarPlayState();
    await this.testNotificationManager();
    
    this.log('🏁 CarPlay Full Diagnostic Complete!');
    
    return this.getLogs();
  }

  getLogs() {
    return [...this.logs];
  }

  clearLogs() {
    this.logs = [];
    this.log('🧹 Logs cleared');
  }

  exportLogs() {
    const logText = this.logs
      .map(log => `[${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}`)
      .join('\n');
    
    return logText;
  }

  // Monitor bridge events for a specific duration
  monitorEvents(durationMs = 10000) {
    this.log(`🔍 Monitoring CarPlay events for ${durationMs}ms...`);
    
    const { CarPlayEventEmitter, MaxRCTCarPlayNotificationManager } = NativeModules;
    const listeners = [];
    
    if (CarPlayEventEmitter) {
      const emitter = new NativeEventEmitter(CarPlayEventEmitter);
      
      const events = ['CarPlayForeground', 'CarPlayBackground', 'CarPlayConnection'];
      events.forEach(eventName => {
        const listener = emitter.addListener(eventName, (data) => {
          this.log(`📡 Event received: ${eventName} - ${JSON.stringify(data)}`);
        });
        listeners.push(listener);
      });
    }
    
    if (MaxRCTCarPlayNotificationManager) {
      const notificationEmitter = new NativeEventEmitter(MaxRCTCarPlayNotificationManager);
      
      const notificationEvents = ['carPlayConnected', 'carPlayDisconnected', 'carPlayNotification'];
      notificationEvents.forEach(eventName => {
        const listener = notificationEmitter.addListener(eventName, (data) => {
          this.log(`📡 Notification event received: ${eventName} - ${JSON.stringify(data)}`);
        });
        listeners.push(listener);
      });
    }
    
    // Clean up after duration
    setTimeout(() => {
      listeners.forEach(listener => {
        try {
          listener.remove();
        } catch (error) {
          this.log(`⚠️ Error removing listener: ${error.message}`, 'warning');
        }
      });
      this.log('🔍 Event monitoring stopped');
    }, durationMs);
    
    return () => {
      listeners.forEach(listener => {
        try {
          listener.remove();
        } catch (error) {
          this.log(`⚠️ Error removing listener: ${error.message}`, 'warning');
        }
      });
      this.log('🔍 Event monitoring stopped (manual)');
    };
  }
}

// Create singleton instance
const carPlayDebugger = new CarPlayDebugger();

export default carPlayDebugger;

// Export convenience methods
export const {
  runFullDiagnostic,
  checkNativeModules,
  testEventEmitters,
  testCarPlayState,
  monitorEvents,
  getLogs,
  clearLogs,
  exportLogs
} = carPlayDebugger;
