# CarPlay State Management

This document describes the enhanced CarPlay state management system for detecting foreground/background states and connection status.

## Overview

The CarPlay state management system provides:
- Real-time foreground/background state detection
- Connection status monitoring
- React hooks for easy integration
- Event-driven architecture
- Debugging tools and components

## System Architecture

### Native iOS Components

#### CarPlaySceneDelegate
- **File**: `ios/chillbaby/CarPlaySceneDelegate.h/m`
- **Purpose**: Handles CarPlay scene lifecycle events
- **Key Features**:
  - Proper scene lifecycle management
  - Foreground/background state tracking
  - Integration with notification manager
  - Event emission to React Native

#### CarPlayEventEmitter
- **File**: `ios/chillbaby/CarPlayEventEmitter.h/m`
- **Purpose**: Bridge between iOS and React Native for CarPlay events
- **Events**:
  - `CarPlayForeground`: When CarPlay enters foreground
  - `CarPlayBackground`: When CarPlay enters background
  - `CarPlayConnection`: When CarPlay connects/disconnects

### React Native Components

#### CarPlayStateManager
- **File**: `app/services/CarPlayStateManager.js`
- **Purpose**: Singleton service for managing CarPlay state
- **Features**:
  - Event listener management
  - State caching
  - Listener notification system

#### React Hooks
- **File**: `app/hooks/useCarPlayState.js`
- **Hooks Available**:
  - `useCarPlayState()`: Full state management
  - `useCarPlayForeground()`: Simple foreground detection
  - `useCarPlayConnection()`: Simple connection detection

## Usage Examples

### Basic Usage

```javascript
import { useCarPlayState } from '../hooks/useCarPlayState';

const MyComponent = () => {
  const { isForeground, isConnected } = useCarPlayState();
  
  if (!isConnected) {
    return <Text>CarPlay not connected</Text>;
  }
  
  return (
    <View>
      <Text>CarPlay is {isForeground ? 'foreground' : 'background'}</Text>
    </View>
  );
};
```

### Advanced Usage with State Changes

```javascript
import { useCarPlayStateWithCallback } from '../hooks/useCarPlayState';

const MyComponent = () => {
  const carPlayState = useCarPlayStateWithCallback((state) => {
    console.log('CarPlay state changed:', state);
    
    if (state.isForeground) {
      // Show CarPlay UI
    } else {
      // Hide CarPlay UI
    }
  });
  
  return <CarPlayUI visible={carPlayState.isForeground} />;
};
```

### Simple Hooks

```javascript
import { useCarPlayForeground, useCarPlayConnection } from '../hooks/useCarPlayState';

const MyComponent = () => {
  const isForeground = useCarPlayForeground();
  const isConnected = useCarPlayConnection();
  
  return (
    <View>
      <Text>Connected: {isConnected ? 'Yes' : 'No'}</Text>
      <Text>Foreground: {isForeground ? 'Yes' : 'No'}</Text>
    </View>
  );
};
```

## Components

### CarPlayStatusIndicator

A ready-to-use component for displaying CarPlay status:

```javascript
import CarPlayStatusIndicator from '../components/CarPlayStatusIndicator';

const MyScreen = () => (
  <View>
    <CarPlayStatusIndicator showDebugInfo={true} />
  </View>
);
```

## API Reference

### CarPlayStateManager Methods

- `getCurrentState()`: Get current CarPlay state
- `isInForeground()`: Check if CarPlay is in foreground
- `isConnected()`: Check if CarPlay is connected
- `addStateListener(callback)`: Add state change listener
- `getCarPlayInfo()`: Get detailed CarPlay information

### useCarPlayState Hook Returns

```javascript
{
  isForeground: boolean,      // Is CarPlay in foreground
  isConnected: boolean,       // Is CarPlay connected
  isBackground: boolean,      // Is CarPlay in background
  timestamp: number,          // Last update timestamp
  lastStateChange: number,    // Last state change timestamp
  isLoading: boolean,         // Is state loading
  refreshState: function,     // Refresh state manually
  getCarPlayInfo: function,   // Get detailed info
  carPlayState: object        // Full state object
}
```

## Events

### CarPlayForeground
Emitted when CarPlay enters foreground state.

**Payload**:
```javascript
{
  isForeground: true,
  timestamp: 1234567890,
  state: "foreground"
}
```

### CarPlayBackground
Emitted when CarPlay enters background state.

**Payload**:
```javascript
{
  isForeground: false,
  timestamp: 1234567890,
  state: "background"
}
```

### CarPlayConnection
Emitted when CarPlay connection status changes.

**Payload**:
```javascript
{
  connected: true/false,
  timestamp: 1234567890
}
```

## Debugging

### Enable Debug Logging
The system includes comprehensive logging. Look for logs with 🚗 emoji.

### Debug Component
Use `CarPlayStatusIndicator` with `showDebugInfo={true}` for real-time debugging.

### Manual State Refresh
```javascript
const { refreshState } = useCarPlayState();
await refreshState();
```

## Version Information

- **App Version**: 1.0.2
- **iOS Deployment Target**: 13.4
- **CarPlay Framework**: react-native-carplay
- **Supported iOS**: 13.4+

## Troubleshooting

### Common Issues

1. **Events not received**: Check if bridge is properly initialized
2. **State not updating**: Ensure component is properly mounted
3. **Connection issues**: Verify CarPlay entitlements and Info.plist configuration

### Verification Steps

1. Check native logs for CarPlay events
2. Use debug component to verify state
3. Test with actual CarPlay device/simulator
4. Verify Info.plist CarPlay configuration

## Migration from Old System

If you're migrating from the old CarPlay state system:

1. Replace direct event listeners with hooks
2. Use `CarPlayStateManager` instead of manual state management
3. Update components to use new hook system
4. Remove duplicate event listener code

## Best Practices

1. Use appropriate hook for your needs (simple vs full state)
2. Handle loading states properly
3. Clean up listeners in useEffect cleanup
4. Use debug components during development
5. Test on actual CarPlay devices
